// 调试padding导致换行的问题
// 在浏览器控制台中运行这个脚本来调试

console.log('=== 调试Padding导致换行问题 ===');

// 检查WrapStrategy枚举值
console.log('WrapStrategy枚举值:');
console.log('UNSPECIFIED:', 0);
console.log('OVERFLOW:', 1);
console.log('CLIP:', 2);
console.log('WRAP:', 3);

// 检查默认样式
console.log('\n=== 检查默认样式 ===');
const univerAPI = window.univerAPI;
if (univerAPI) {
    const workbook = univerAPI.getActiveWorkbook();
    const worksheet = workbook?.getActiveSheet();
    
    if (worksheet) {
        // 创建两个单元格进行对比
        const cellA1 = worksheet.getRange(0, 0); // A1 - 无padding
        const cellB1 = worksheet.getRange(0, 1); // B1 - 有padding
        
        // 设置B1的padding
        cellB1.setValue('测试文本');
        cellB1.setStyle({
            pd: { l: 10, r: 10, t: 5, b: 5 }
        });
        
        // 检查样式
        console.log('A1单元格样式:', cellA1.getStyle());
        console.log('B1单元格样式:', cellB1.getStyle());
        
        // 检查换行策略
        console.log('A1换行策略:', cellA1.getWrapStrategy());
        console.log('B1换行策略:', cellB1.getWrapStrategy());
    }
} else {
    console.log('univerAPI不可用');
}

// 监听编辑事件
console.log('\n=== 监听编辑事件 ===');
console.log('请手动编辑单元格，观察控制台输出...');
